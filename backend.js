require('dotenv').config();
const express = require('express');
const fetch = require('node-fetch');
const session = require('express-session');
const cors = require('cors');
const querystring = require('querystring');

const app = express();
const PORT = process.env.PORT || 3000;

// Session Konfiguration
app.use(session({
  secret: process.env.SESSION_SECRET || 'secret',
  resave: false,
  saveUninitialized: false,
  proxy: true,   // <--- wichtig, weil du hinter Nginx sitzt
  cookie: {
    secure: true,       // weil du über HTTPS gehst
    httpOnly: true,
    sameSite: 'lax'
  }
}));


// CORS Konfiguration
app.use(cors({
  origin: process.env.FRONTEND_ORIGIN || 'https://xenny.news',
  credentials: true,
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

app.use(express.json());

const SPOTIFY_AUTH_URL = 'https://accounts.spotify.com/authorize';
const SPOTIFY_TOKEN_URL = 'https://accounts.spotify.com/api/token';
const SPOTIFY_API_BASE = 'https://api.spotify.com/v1';
const SCOPES = 'playlist-read-private playlist-read-collaborative user-read-private';

// ---------------------------
// Auth Routes mit Prefix
// ---------------------------
app.get('/auth/login', (req, res) => {
  const state = Math.random().toString(36).substring(2, 15);
  req.session.oauthState = state;

  const params = new URLSearchParams({
    response_type: 'code',
    client_id: process.env.SPOTIFY_CLIENT_ID,
    scope: SCOPES,
    redirect_uri: process.env.SPOTIFY_REDIRECT_URI,
    state,
    show_dialog: true
  });

  res.redirect(`${SPOTIFY_AUTH_URL}?${params.toString()}`);
});

app.get('/auth/callback', async (req, res) => {
  const { code, state, error } = req.query;
  if (error) return res.status(400).send(`Spotify auth error: ${error}`);
  if (state !== req.session.oauthState) return res.status(400).send('Invalid state');

  try {
    const body = new URLSearchParams();
    body.append('grant_type', 'authorization_code');
    body.append('code', code);
    body.append('redirect_uri', process.env.SPOTIFY_REDIRECT_URI);

    const basic = Buffer.from(`${process.env.SPOTIFY_CLIENT_ID}:${process.env.SPOTIFY_CLIENT_SECRET}`).toString('base64');

    const tokenResp = await fetch(SPOTIFY_TOKEN_URL, {
      method: 'POST',
      headers: {
        Authorization: `Basic ${basic}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: body.toString()
    });

    if (!tokenResp.ok) throw new Error(await tokenResp.text());
    const tokenJson = await tokenResp.json();

    req.session.access_token = tokenJson.access_token;
    req.session.refresh_token = tokenJson.refresh_token;
    req.session.token_expires_in = Date.now() + (tokenJson.expires_in * 1000);

    const frontend = process.env.FRONTEND_ORIGIN || 'https://xenny.news';
    res.redirect(`${frontend}/PlaylistHigherLower/index.html?authed=true`);
  } catch (err) {
    console.error(err);
    res.status(500).send('Auth callback failed');
  }
});

app.get('/auth/status', (req, res) => {
  res.json({ authed: !!req.session.access_token });
});

// ---------------------------
// Hier kämen dann deine weiteren API-Routen
// z. B. /playlistHigherLower/api/playlist usw.
// ---------------------------

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
