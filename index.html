<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Spotify Higher/Lower Game</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1DB954, #191414);
            min-height: 100vh;
            color: white;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 40px; }
        .header h1 { font-size: 3em; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .login-section {
            text-align: center; padding: 40px; background: rgba(0,0,0,0.3);
            border-radius: 20px; backdrop-filter: blur(10px);
        }
        .spotify-btn {
            background: #1DB954; color: white; border: none; padding: 15px 30px;
            font-size: 18px; border-radius: 50px; cursor: pointer; transition: all 0.3s ease;
            text-decoration: none; display: inline-block; margin: 10px;
        }
        .spotify-btn:hover { background: #1ed760; transform: translateY(-2px); box-shadow: 0 5px 15px rgba(29,185,84,0.4); }
        .playlist-section { display: none; text-align: center; margin: 20px 0; }
        .playlist-input {
            padding: 12px 20px; font-size: 16px; border: none; border-radius: 25px;
            width: 400px; max-width: 90%; margin: 10px; background: rgba(255,255,255,0.1);
            color: white; backdrop-filter: blur(10px);
        }
        .playlist-input::placeholder { color: rgba(255,255,255,0.7); }
        .game-container { display: none; margin-top: 40px; }
        .score { text-align: center; font-size: 24px; margin-bottom: 30px; font-weight: bold; }
        .game-board { display: flex; gap: 20px; justify-content: center; align-items: stretch; }
        .song-card {
            flex: 1; max-width: 400px; background: rgba(0,0,0,0.4); border-radius: 20px; padding: 30px;
            text-align: center; backdrop-filter: blur(10px); transition: all 0.3s ease; cursor: pointer;
        }
        .song-card:hover { transform: translateY(-5px); box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        .song-card.left { border: 3px solid #1DB954; }
        .song-card.right { border: 3px solid #FFA500; }
        .song-image { width: 200px; height: 200px; border-radius: 15px; margin: 0 auto 20px; object-fit: cover; box-shadow: 0 5px 20px rgba(0,0,0,0.3); }
        .song-title { font-size: 20px; font-weight: bold; margin-bottom: 10px; line-height: 1.3; }
        .song-artist { font-size: 16px; color: rgba(255,255,255,0.8); margin-bottom: 20px; }
        .streams { font-size: 24px; font-weight: bold; color: #1DB954; margin: 20px 0; }
        .vs { align-self: center; font-size: 3em; font-weight: bold; text-shadow: 2px 2px 4px rgba(0,0,0,0.5); margin: 0 20px; }
        .choice-buttons { display: flex; gap: 10px; justify-content: center; margin-top: 20px; }
        .choice-btn {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4); color: white; border: none; padding: 15px 25px;
            font-size: 18px; font-weight: bold; border-radius: 15px; cursor: pointer; transition: all 0.3s ease;
        }
        .choice-btn:hover { transform: scale(1.05); box-shadow: 0 5px 15px rgba(0,0,0,0.3); }
        .game-over { display: none; text-align: center; padding: 40px; background: rgba(0,0,0,0.5); border-radius: 20px; backdrop-filter: blur(10px); }
        .game-over h2 { font-size: 3em; margin-bottom: 20px; color: #FF6B6B; }
        .final-score { font-size: 2em; margin: 20px 0; color: #1DB954; }
        .loading { text-align: center; padding: 40px; font-size: 18px; }
        .spinner { border: 4px solid rgba(255,255,255,0.3); border-top: 4px solid #1DB954; border-radius: 50%; width: 50px; height: 50px; animation: spin 1s linear infinite; margin: 20px auto; }
        @keyframes spin { 0% { transform: rotate(0deg);} 100% { transform: rotate(360deg);} }
        @media (max-width: 768px) {
            .game-board { flex-direction: column; align-items: center; }
            .vs { transform: rotate(90deg); margin: 10px 0; }
            .song-card { max-width: 300px; }
            .header h1 { font-size: 2em; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎵 Spotify Higher/Lower 🎵</h1>
            <p>Rate die Stream-Zahlen deiner Lieblings-Playlist!</p>
        </div>

        <div id="loginSection" class="login-section">
            <h2>Mit Spotify anmelden</h2>
            <p>Melde dich an, um mit deinen Playlists zu spielen!</p>
            <button class="spotify-btn" onclick="loginWithSpotify()">
                🎵 Mit Spotify anmelden
            </button>
        </div>

        <div id="playlistSection" class="playlist-section">
            <h3>Playlist auswählen</h3>
            <p>Kopiere eine Spotify Playlist URL hier ein:</p>
            <input type="text" id="playlistInput" class="playlist-input" 
                   placeholder="z.B. https://open.spotify.com/playlist/37i9dQZF1DXcBWIGoYBM5M">
            <br>
            <button class="spotify-btn" onclick="loadPlaylist()">Playlist laden</button>
            <div id="loading" class="loading" style="display: none;">
                <div class="spinner"></div>
                <p>Lade Playlist und analysiere Tracks...</p>
            </div>
        </div>

        <div id="gameContainer" class="game-container">
            <div class="score">Score: <span id="score">0</span></div>
            <div class="game-board">
                <div class="song-card left" id="leftCard">
                    <img id="leftImage" class="song-image" src="" alt="Album Cover">
                    <div class="song-title" id="leftTitle"></div>
                    <div class="song-artist" id="leftArtist"></div>
                    <div class="streams" id="leftStreams"></div>
                </div>

                <div class="vs">VS</div>

                <div class="song-card right" id="rightCard">
                    <img id="rightImage" class="song-image" src="" alt="Album Cover">
                    <div class="song-title" id="rightTitle"></div>
                    <div class="song-artist" id="rightArtist"></div>
                    <div class="streams" id="rightStreams" style="display: none;"></div>

                    <div class="choice-buttons">
                        <button class="choice-btn" onclick="makeChoice('higher')">📈 MEHR Streams</button>
                        <button class="choice-btn" onclick="makeChoice('lower')">📉 WENIGER Streams</button>
                    </div>
                </div>
            </div>
        </div>

        <div id="gameOver" class="game-over">
            <h2>GAME OVER!</h2>
            <div class="final-score">Dein Score: <span id="finalScore">0</span></div>
            <p>Gut gespielt! Möchtest du nochmal spielen?</p>
            <button class="spotify-btn" onclick="restartGame()">Nochmal spielen</button>
        </div>
    </div>

    <script>
        /*********************************************************************
         * Konfiguration
         * Setze BACKEND_URL auf die URL deines Backends.
         * Wenn Frontend und Backend dieselbe Origin haben, lasse ''.
         ********************************************************************/
        const BACKEND_URL = ''; // Beispiel: 'https://dein-backend.tld'  (ohne Slash am Ende)

        // Game state
        let playlistTracks = [];
        let currentSongs = [];
        let score = 0;
        let currentIndex = 0;

        // Hilfsfunktion: kompletter fetch-Pfad
        function backend(path) {
            if (!BACKEND_URL) return path; // relative Pfade (gleiche Origin)
            // ensure path starts with /
            if (!path.startsWith('/')) path = '/' + path;
            return BACKEND_URL + path;
        }

        // --- Login: öffnet Backend-Route die zu Spotify weiterleitet ---
        function loginWithSpotify() {
            const loginUrl = backend('/playlistHigherLower/api/auth/login');
            window.location.href = loginUrl;
        }

        // --- Beim Laden prüfen ob bereits angemeldet ---
        window.addEventListener('load', async () => {
            try {
                // Wenn Spotify nach dem Login hierher redirectet (z.B. frontend?authed=true),
                // dann prüfen wir den Auth-Status beim Backend.
                const statusResp = await fetch(backend('/playlistHigherLower/api/auth/status'), {
                    credentials: 'include'
                });
                if (statusResp.ok) {
                    const status = await statusResp.json();
                    if (status.authed) {
                        document.getElementById('loginSection').style.display = 'none';
                        document.getElementById('playlistSection').style.display = 'block';
                        // optional: entferne authed param aus URL
                        const url = new URL(window.location.href);
                        if (url.searchParams.get('authed')) {
                            url.searchParams.delete('authed');
                            window.history.replaceState({}, document.title, url.toString());
                        }
                        return;
                    }
                }
                // not authed -> show login
                document.getElementById('loginSection').style.display = 'block';
                document.getElementById('playlistSection').style.display = 'none';
            } catch (err) {
                console.error('Auth status check failed', err);
                // Fallback: zeige Login (Frontend funktioniert auch ohne Backend-Login, Benutzer erfährt Fehler)
                document.getElementById('loginSection').style.display = 'block';
                document.getElementById('playlistSection').style.display = 'none';
            }
        });

        // --- Playlist laden (wird vom Backend vollständig aufbereitet) ---
        async function loadPlaylist() {
            const playlistInput = document.getElementById('playlistInput').value.trim();
            if (!playlistInput) {
                alert('Bitte gib eine Spotify Playlist URL ein.');
                return;
            }

            document.getElementById('loading').style.display = 'block';

            try {
                // Anfrage an Backend: Backend akzeptiert entweder ?url=... oder ?id=...
                const url = backend(`/playlistHigherLower/playlist?url=${encodeURIComponent(playlistInput)}`);
                const resp = await fetch(url, {
                    credentials: 'include' // sehr wichtig: sendet Session-Cookie mit
                });

                if (!resp.ok) {
                    const txt = await resp.text();
                    throw new Error(txt || 'Fehler beim Backend');
                }

                const data = await resp.json();
                // Backend liefert { playlist: 'id', tracks: [ { id, name, artist, image, popularity, streams } ] }
                playlistTracks = data.tracks || [];

                if (!playlistTracks || playlistTracks.length < 2) {
                    throw new Error('Playlist muss mindestens 2 Tracks haben oder das Backend konnte keine Tracks lesen.');
                }

                shuffleArray(playlistTracks);
                document.getElementById('loading').style.display = 'none';
                document.getElementById('playlistSection').style.display = 'none';
                document.getElementById('gameContainer').style.display = 'block';

                startGame();
            } catch (err) {
                console.error('Fehler beim Laden der Playlist:', err);
                document.getElementById('loading').style.display = 'none';
                alert('Fehler beim Laden der Playlist: ' + (err.message || err));
            }
        }

        // --- Game logic (gleich wie vorher, benutzt jetzt playlistTracks direkt) ---
        function shuffleArray(array) {
            for (let i = array.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [array[i], array[j]] = [array[j], array[i]];
            }
        }

        function startGame() {
            score = 0;
            currentIndex = 0;
            updateScore();
            loadNextSongs();
        }

        function loadNextSongs() {
            if (playlistTracks.length < 2) {
                gameOver();
                return;
            }

            if (currentIndex + 1 >= playlistTracks.length) {
                shuffleArray(playlistTracks);
                currentIndex = 0;
            }

            const leftSong = currentIndex === 0 ? playlistTracks[0] : currentSongs[1];
            const rightSong = playlistTracks[currentIndex + 1];
            currentSongs = [leftSong, rightSong];

            // Update left card
            document.getElementById('leftImage').src = leftSong.image || 'https://via.placeholder.com/200/1DB954/FFFFFF?text=No+Image';
            document.getElementById('leftTitle').textContent = leftSong.name || 'Unbekannt';
            document.getElementById('leftArtist').textContent = leftSong.artist || '';
            document.getElementById('leftStreams').textContent = formatStreams(leftSong.streams || 0);

            // Update right card
            document.getElementById('rightImage').src = rightSong.image || 'https://via.placeholder.com/200/FFA500/FFFFFF?text=No+Image';
            document.getElementById('rightTitle').textContent = rightSong.name || 'Unbekannt';
            document.getElementById('rightArtist').textContent = rightSong.artist || '';
            document.getElementById('rightStreams').textContent = formatStreams(rightSong.streams || 0);
            document.getElementById('rightStreams').style.display = 'none';

            // Enable choice buttons
            const buttons = document.querySelectorAll('.choice-btn');
            buttons.forEach(btn => btn.disabled = false);
        }

        function formatStreams(streams) {
            if (!streams && streams !== 0) return '—';
            if (streams >= 1000000000) {
                return (streams / 1000000000).toFixed(1) + 'B Streams';
            } else if (streams >= 1000000) {
                return (streams / 1000000).toFixed(1) + 'M Streams';
            } else if (streams >= 1000) {
                return (streams / 1000).toFixed(1) + 'K Streams';
            } else {
                return streams + ' Streams';
            }
        }

        function makeChoice(choice) {
            const leftStreams = currentSongs[0].streams || 0;
            const rightStreams = currentSongs[1].streams || 0;

            // Zeige die Streams des rechten Songs
            document.getElementById('rightStreams').style.display = 'block';

            // Disable buttons
            const buttons = document.querySelectorAll('.choice-btn');
            buttons.forEach(btn => btn.disabled = true);

            // Check if choice is correct
            let isCorrect = false;
            if (choice === 'higher' && rightStreams >= leftStreams) {
                isCorrect = true;
            } else if (choice === 'lower' && rightStreams <= leftStreams) {
                isCorrect = true;
            }

            setTimeout(() => {
                if (isCorrect) {
                    score++;
                    updateScore();
                    currentIndex++;
                    loadNextSongs();
                } else {
                    gameOver();
                }
            }, 1500);
        }

        function updateScore() {
            document.getElementById('score').textContent = score;
        }

        function gameOver() {
            document.getElementById('finalScore').textContent = score;
            document.getElementById('gameContainer').style.display = 'none';
            document.getElementById('gameOver').style.display = 'block';
        }

        function restartGame() {
            document.getElementById('gameOver').style.display = 'none';
            document.getElementById('gameContainer').style.display = 'block';
            startGame();
        }
    </script>
</body>
</html>